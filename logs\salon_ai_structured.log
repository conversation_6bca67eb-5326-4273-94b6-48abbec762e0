 
{"timestamp": 1755011956.953664, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011956.955249, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1286561, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1313152, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 60, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.135483, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 62, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1390917, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1464236, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 66, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1487565, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 68, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1508458, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 70, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1551833, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 72, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1621888, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 74, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.166801, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1706886, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.184792, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 80, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.695342, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 64, "elapsed_time": 1.55, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.6952832, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7003922, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 66, "elapsed_time": 1.55, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.70027, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7141798, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 58, "elapsed_time": 1.58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.714194, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7369378, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 68, "elapsed_time": 1.59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7369301, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7381742, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7407305, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 60, "elapsed_time": 1.61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.772819, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 72, "elapsed_time": 1.62, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7727983, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7729063, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7733305, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.773279, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7741244, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 62, "elapsed_time": 1.64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.775819, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 78, "elapsed_time": 1.6, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7769794, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 70, "elapsed_time": 1.63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8224936, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 76, "elapsed_time": 1.65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8224928, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.822464, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8239799, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 80, "elapsed_time": 1.64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8591094, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 74, "elapsed_time": 1.7, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.859061, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8605416, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011961.9963198, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_2yvBMQ3rMzEF", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011975.4980526, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 28, "message": "Test logging setup started", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011975.4980526, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "in_progress", "duration_ms": 30, "user_id": "test_user_9", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011976.011897, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "shutdown", "component": "database", "status": "error", "error_type": "TestError", "error_code": 4806, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011976.5131247, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "in_progress", "duration_ms": 450, "user_id": "test_user_75", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011977.014143, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "in_progress", "duration_ms": 942, "user_id": "test_user_73", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011977.515134, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in livekit", "taskName": null, "event": "performance_check", "component": "livekit", "status": "in_progress", "duration_ms": 955, "user_id": "test_user_26", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011978.017005, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in database", "taskName": null, "event": "user_action", "component": "database", "status": "in_progress", "duration_ms": 402, "user_id": "test_user_72", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011978.5181708, "level": "WARNING", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in voice_agent", "taskName": null, "event": "error_recovery", "component": "voice_agent", "status": "warning", "threshold_value": 75, "current_value": 99, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011979.018705, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "success", "duration_ms": 670, "user_id": "test_user_52", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011979.5199282, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 1332, "retry_count": 3, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011980.0207834, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "success", "duration_ms": 655, "user_id": "test_user_35", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011980.521823, "level": "WARNING", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "warning", "threshold_value": 96, "current_value": 93, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011981.0231576, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "warning", "threshold_value": 94, "current_value": 87, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011981.5258002, "level": "WARNING", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in livekit", "taskName": null, "event": "startup", "component": "livekit", "status": "warning", "threshold_value": 83, "current_value": 110, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011982.0273342, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in database", "taskName": null, "event": "user_action", "component": "database", "status": "success", "duration_ms": 936, "user_id": "test_user_49", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011982.5282538, "level": "WARNING", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in livekit", "taskName": null, "event": "startup", "component": "livekit", "status": "warning", "threshold_value": 76, "current_value": 109, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011983.0290954, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "success", "duration_ms": 813, "user_id": "test_user_60", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011983.5301604, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in voice_agent", "taskName": null, "event": "shutdown", "component": "voice_agent", "status": "in_progress", "duration_ms": 127, "user_id": "test_user_23", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011984.0310113, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 2031, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011984.5317018, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "success", "duration_ms": 952, "user_id": "test_user_50", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.032589, "level": "ERROR", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "error", "error_type": "TestError", "error_code": 5236, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5337758, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 83, "message": "Application startup initiated", "taskName": null, "event": "application_startup", "component": "main", "status": "in_progress", "version": "1.0.0", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5337758, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 90, "message": "Database connection established", "taskName": null, "event": "database_connection", "component": "database", "status": "success", "connection_pool_size": 10, "connection_timeout": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5337758, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 98, "message": "Voice agent initialized", "taskName": null, "event": "voice_agent_init", "component": "voice_agent", "status": "success", "model": "gpt-4", "voice_provider": "elevenlabs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5337758, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_5744", "phone_number": "+13579222526", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.537557, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_5744", "phone_number": "+13579222526", "call_duration": 218, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.537557, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_7047", "phone_number": "+11702486879", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011987.5387695, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_7047", "phone_number": "+11702486879", "call_duration": 91, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011987.5387695, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_9096", "phone_number": "+19975893704", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011988.540046, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_9096", "phone_number": "+19975893704", "call_duration": 202, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011988.540046, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_3846", "phone_number": "+14308460123", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011989.5412755, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_3846", "phone_number": "+14308460123", "call_duration": 279, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011989.5412755, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_9264", "phone_number": "+13768932391", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_9264", "phone_number": "+13768932391", "call_duration": 293, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 133, "message": "Database connection lost", "taskName": null, "event": "database_error", "component": "database", "status": "error", "error_type": "ConnectionError", "error_message": "Connection timeout after 30 seconds", "retry_attempt": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 142, "message": "Voice synthesis failed", "taskName": null, "event": "voice_synthesis_error", "component": "voice_agent", "status": "error", "error_type": "APIError", "provider": "elevenlabs", "error_code": 429, "rate_limit_exceeded": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 153, "message": "Performance metrics collected", "taskName": null, "event": "metrics_collection", "component": "metrics", "status": "success", "cpu_usage": 61, "memory_usage": 71, "active_calls": 2, "response_time_ms": 118, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 163, "message": "Test logging completed", "taskName": null, "event": "test_completed", "component": "test", "status": "success", "total_logs_generated": 30, "test_duration": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8850734, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_2yvBMQ3rMzEF", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.901625, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_2yvBMQ3rMzEF", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012506.8811305, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012506.8826497, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.001554, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.007603, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.011262, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.017709, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0243924, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0313582, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0401628, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.046629, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0543647, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0719957, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0748048, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0849612, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3097677, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3117127, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3391411, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3083074, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.345061, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 65, "elapsed_time": 2.32, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3763707, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 59, "elapsed_time": 2.37, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3774242, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 61, "elapsed_time": 2.36, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3786576, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 2.38, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.4845157, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 67, "elapsed_time": 2.45, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.485841, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.4860895, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.5241146, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 77, "elapsed_time": 2.45, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.5497503, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 69, "elapsed_time": 2.51, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.550044, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6005406, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 73, "elapsed_time": 2.54, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6019113, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 79, "elapsed_time": 2.52, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.600758, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6012552, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6036203, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 63, "elapsed_time": 2.58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.600571, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6055973, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 75, "elapsed_time": 2.53, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6062021, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6505828, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 71, "elapsed_time": 2.6, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.651124, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6568353, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012512.7891183, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_rcmLAMJ3vgr4", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012523.9501805, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_rcmLAMJ3vgr4", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012523.9577618, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_rcmLAMJ3vgr4", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012640.3795474, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012640.3805265, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4095612, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4112935, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4134548, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4155195, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.418358, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4205225, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4223197, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.424853, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4271479, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4321442, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.435749, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4404876, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6414442, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6417398, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6430566, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6424067, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 63, "elapsed_time": 2.23, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6446784, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6475246, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 69, "elapsed_time": 2.22, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6531193, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 67, "elapsed_time": 2.23, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6545296, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 71, "elapsed_time": 2.23, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.66751, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 61, "elapsed_time": 2.25, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6671205, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6839964, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 2.27, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6840205, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7000666, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 65, "elapsed_time": 2.28, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6999755, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7248707, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 59, "elapsed_time": 2.31, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7249427, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7651904, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 73, "elapsed_time": 2.34, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7652502, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.765582, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7653384, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7654147, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7682457, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 79, "elapsed_time": 2.33, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7696047, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 75, "elapsed_time": 2.34, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.77046, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 77, "elapsed_time": 2.33, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7724602, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012645.966038, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_SoybGcSibd8U", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013536.3564556, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_SoybGcSibd8U", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013536.3684561, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_SoybGcSibd8U", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013557.3717754, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013557.3731635, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7347107, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7425332, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 60, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7501404, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 62, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7693994, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.770782, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.774108, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 68, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7782776, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 70, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.783964, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 72, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.788019, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 74, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.792779, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.8176985, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.818801, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.598764, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6022542, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 58, "elapsed_time": 2.87, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.615199, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6226244, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6380093, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 65, "elapsed_time": 2.86, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6575804, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 76, "elapsed_time": 2.86, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.660848, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6634488, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 62, "elapsed_time": 2.91, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6694782, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.670663, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 68, "elapsed_time": 2.89, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6721876, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6752162, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 60, "elapsed_time": 2.93, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.714114, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 72, "elapsed_time": 2.93, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7142751, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.73041, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 79, "elapsed_time": 2.91, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7308483, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7325559, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 70, "elapsed_time": 2.95, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.732678, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7589421, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 74, "elapsed_time": 2.97, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7590518, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.776447, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 64, "elapsed_time": 3.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.776528, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.8860567, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 78, "elapsed_time": 3.07, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.8865297, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.8946936, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013563.2591195, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_cXCWKAyWQKJD", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "India", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015885.391704, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-142", "id": "AW_cXCWKAyWQKJD", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015885.4226727, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-143", "id": "AW_cXCWKAyWQKJD", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015886.3673573, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Future exception was never retrieved\nfuture: <Future finished exception=ClientConnectionError('Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)')>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/asyncio/sslproto.py\", line 651, in _do_shutdown\n    self._sslobj.unwrap()\n  File \"/usr/local/lib/python3.12/ssl.py\", line 920, in unwrap\n    return self._sslobj.shutdown()\n           ^^^^^^^^^^^^^^^^^^^^^^^\nssl.SSLError: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)\n\nThe above exception was the direct cause of the following exception:\n\naiohttp.client_exceptions.ClientConnectionError: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015901.3312182, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015901.3323727, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.6914678, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.6984174, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 60, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.703993, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 62, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7139695, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7216494, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 66, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7280462, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 68, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7325528, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 70, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7404327, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 72, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7524219, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 74, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7623208, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7684, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7883902, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 80, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.6174984, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 60, "elapsed_time": 3.92, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.6148455, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.8020327, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 78, "elapsed_time": 4.03, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.8029513, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.8763657, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 64, "elapsed_time": 4.16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.8786561, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.960703, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 76, "elapsed_time": 4.2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.9631238, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 62, "elapsed_time": 4.26, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.9617023, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.971358, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 74, "elapsed_time": 4.22, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.9664145, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.97279, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.064779, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 70, "elapsed_time": 4.33, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.0664816, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 58, "elapsed_time": 4.37, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.0654085, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.065993, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.1038005, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 68, "elapsed_time": 4.37, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.1061466, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 66, "elapsed_time": 4.38, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.1046748, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.1069443, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.2292182, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 80, "elapsed_time": 4.44, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.229682, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.4543839, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 72, "elapsed_time": 4.71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.455088, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.4647696, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015909.7995815, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_RL8BMBDjUjdp", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015934.4264512, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-143", "job_id": "AJ_dxm4BtwG62R2", "dispatch_id": "", "room_name": "playground-URrc-PLuT", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015934.8690968, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015934.9210029, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-145", "pid": 255, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0165818, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0178354, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0165818, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0178354, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0540082, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=15&auto_subscribe=1&adaptive_stream=0&version=1.0.8&access_token=...", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.0456128, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-145", "pid": 255, "elapsed_time": 1.12, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.0455906, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.2431207, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.244807, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.2456539, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.2464495, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.4819875, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.4828925, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.4840121, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.484969, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.9892159, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015945.031596, "level": "WARNING", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_warn_not_connected_task", "line": 268, "message": "The room connection was not established within 10 seconds after calling job_entry. This may indicate that job_ctx.connect() was not called. ", "taskName": "Task-7", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015945.031596, "level": "WARNING", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_warn_not_connected_task", "line": 268, "message": "The room connection was not established within 10 seconds after calling job_entry. This may indicate that job_ctx.connect() was not called. ", "taskName": "Task-7", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.6198, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981cfa10>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.6198, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981cfa10>", "taskName": "job_user_entrypoint", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.621448, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task finished name='job_user_entrypoint' coro=<entrypoint() done, defined at /app/main.py:85> result=None> took too long: 2.08 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.621448, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task finished name='job_user_entrypoint' coro=<entrypoint() done, defined at /app/main.py:85> result=None> took too long: 2.08 seconds", "taskName": null, "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.6265795, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.876485, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.8771079, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.876485, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.8771079, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.8912117, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.9998653, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M HTTP/1.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0009785, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0020888, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0031059, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0044577, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: AO7Fl/wXoH4FNjd+gNa4rQ==", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0056343, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0065076, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0076716, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.008972, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0098474, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0113428, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0125873, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1116903, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: Some(CloseFrame { code: Normal, reason: \"\" })", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1187341, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1257:livekit::room - disconnected from room with reason: ClientInitiated", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1194055, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1194055, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1250057, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "room disconnected", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.126312, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 315, "message": "process exiting", "taskName": "Task-53", "reason": "room disconnected", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1271853, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1284566, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1271853, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1313872, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1284566, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.133201, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1313872, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.134321, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.263121, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-157", "job_id": "AJ_MUjhMqGGiDGS", "dispatch_id": "", "room_name": "playground-8ev8-M8VI", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.5411859, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.6125557, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-159", "pid": 306, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.6421192, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.643111, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.6421192, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.643111, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.6550512, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=15&auto_subscribe=1&adaptive_stream=0&version=1.0.8&access_token=...", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.9652543, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.966393, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.9672954, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.9683163, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.2435243, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.2448027, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.2464113, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.247676, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.5353107, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.6150076, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-159", "pid": 306, "elapsed_time": 1.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.615032, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015995.5666127, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981d3980>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015995.5666127, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981d3980>", "taskName": "job_user_entrypoint", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015995.5732546, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8669827, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8675778, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8669827, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8675778, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8821728, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9467018, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M HTTP/1.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.947899, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9493628, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9507804, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.952073, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: 4OlwNRDdXwcOr8Uy/LE0bQ==", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9533377, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9541795, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9550054, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9561398, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.95734, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9585032, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9593697, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1122253, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1132493, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1154132, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 315, "message": "process exiting", "taskName": "Task-60", "reason": "", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1190274, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.121434, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1257:livekit::room - disconnected from room with reason: ClientInitiated", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1190274, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.126657, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1277988, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.126657, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1277988, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.129164, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1324897, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.129164, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1341357, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.5853322, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-171", "job_id": "AJ_cyvchDE3kQBq", "dispatch_id": "", "room_name": "playground-11yj-w52v", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.837659, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.8737397, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-173", "pid": 359, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9316955, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9332132, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9316955, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9332132, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9459429, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=15&auto_subscribe=1&adaptive_stream=0&version=1.0.8&access_token=...", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.2771258, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.2784953, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.2795544, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.2808444, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.5594544, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.5604353, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.5618553, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.562885, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.8164387, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.866535, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.8666134, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-173", "pid": 359, "elapsed_time": 0.99, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016059.9200807, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981cfa70>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016059.9200807, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981cfa70>", "taskName": "job_user_entrypoint", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016059.926676, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.1782324, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.1791322, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 8, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.1782324, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.1791322, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 8, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.2006562, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3161592, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M HTTP/1.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3174367, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3188927, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3196874, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.320443, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: iFJ/YA2vkjAjLuDuplRmTw==", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3213294, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3222249, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.323131, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3239417, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.324811, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3255334, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3262753, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4332745, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4400032, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4438498, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4400032, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4452195, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 315, "message": "process exiting", "taskName": "Task-67", "reason": "", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4457562, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4467154, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4457562, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.447682, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4467154, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4488122, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.447682, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4497814, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2253144, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "taskName": "agent_runner", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.233887, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-5", "tid": 26012, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.234897, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-6", "tid": 25008, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2358963, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-7", "tid": 43392, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2358963, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-8", "tid": 40856, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2368975, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-5", "tid": 26012, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.237894, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-6", "tid": 25008, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.239043, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-7", "tid": 43392, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2409933, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-8", "tid": 40856, "elapsed_time": 0.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016279.4394581, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_ZL6jggYhTpFh", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.1842463, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "taskName": "Task-53", "job_id": "AJ_YP8QeJr34UWr", "dispatch_id": "", "room_name": "playground-7sLN-lGgM", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.4762342, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-58", "tid": 41596, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.4762342, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-58", "tid": 41596, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.6129768, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.6129768, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016312.89686, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x000001F7379C70B0>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016313.155659, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016313.156656, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016313.1571722, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016338.4389656, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-185", "job_id": "AJ_A8BJzed8moPR", "dispatch_id": "", "room_name": "playground-Nfqq-o6rp", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016338.7512856, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016338.851267, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-187", "pid": 433, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.0680416, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.0693634, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.0680416, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.0693634, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.110479, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=15&auto_subscribe=1&adaptive_stream=0&version=1.0.8&access_token=...", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.4114804, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.4128094, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.4136822, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.4147031, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.669701, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.6714165, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.6732702, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.676016, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.913719, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016340.5082035, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016340.5083892, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-187", "pid": 433, "elapsed_time": 1.68, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016346.2088637, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981d38c0>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016346.2088637, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981d38c0>", "taskName": "job_user_entrypoint", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016346.2208672, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4285288, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4296272, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 10, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4285288, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4296272, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 10, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4480648, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5195, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M HTTP/1.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5211241, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5221856, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.523052, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5240867, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: 1/E+T4Wg0Nx7D8o7oT7RCw==", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5249228, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5257568, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5275111, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5284781, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5294607, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5306365, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5313911, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7049367, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7128408, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1257:livekit::room - disconnected from room with reason: ClientInitiated", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7143648, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7143648, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7190688, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "room disconnected", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7207153, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 315, "message": "process exiting", "taskName": "Task-74", "reason": "room disconnected", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7223678, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7233868, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7223678, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.724592, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7233868, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7260547, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.724592, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7270532, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016430.2195997, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-199", "id": "AW_RL8BMBDjUjdp", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016430.2277617, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-200", "id": "AW_RL8BMBDjUjdp", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017281.3848157, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017281.3859758, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "preloading plugins", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google", "av"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017283.8216352, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 42, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017283.828726, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 44, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017283.837997, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 46, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017283.8497338, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 48, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0642636, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0642636, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0653896, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0664582, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 42, "elapsed_time": 1.24, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0659118, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.070331, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 46, "elapsed_time": 1.23, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0757985, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 48, "elapsed_time": 1.22, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.078423, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 44, "elapsed_time": 1.25, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.2089708, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "id": "AW_mcow9KsMBFgd", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.4793541, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_2taSuZJsmmjc", "dispatch_id": "", "room_name": "playground-7NOb-EKGt", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.7366498, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8528178, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 108, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8895686, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8908083, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8895686, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8908083, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.941509, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.4833672, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.4912484, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.4997385, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.5057907, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.8806512, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.8843277, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.8886395, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.891143, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017288.2392638, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017289.1598577, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 108, "elapsed_time": 2.31, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017289.1598732, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017294.6460462, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017295.0357025, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.2500541, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-bREO", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.255887, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-bREO", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.2573414, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.258045, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.2590075, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f891d83e780>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.2590075, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f891d83e780>", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.277732, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3342252, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3355856, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3364902, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.337773, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.33892, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: wOlbxN/SU3STXVYaXsjMbg==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3398669, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.340906, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3415709, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3425987, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3433356, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.34412, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3449793, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.552646, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.5593169, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1388:livekit::room - disconnected from room with reason: ClientInitiated", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6135972, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 333, "message": "< HTTP/1.1 101 Switching Protocols", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6145742, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< X-Client-Wire-Protocol: HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6154213, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6167142, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6178274, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Sec-WebSocket-Accept: Qivr2JdyPMlgDxEHatPmg9IEiVY=", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.618843, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Date: Tue, 12 Aug 2025 16:48:16 GMT", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6196961, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Server-Timing: gfet4t7; dur=233", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6204743, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Set-Cookie: S==YBBJ-LPPKlGtW6JuwbdGpkTk4JvEY__fSt8UmqWIfZU; path=/; priority=low", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6214416, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Alt-Svc: h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6222517, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is OPEN", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6234016, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_frame", "line": 719, "message": "> TEXT '{\"setup\": {\"model\": \"models/gemini-2.5-flash-li...dioTranscription\": {}}}' [8291 bytes]", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6255574, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6266985, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6274343, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.628174, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6274343, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6290917, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.628174, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.630107, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-bREO", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6290917, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6312642, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_frame", "line": 719, "message": "> CLOSE 1000 (OK) [2 bytes]", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6324139, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSING", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9102263, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "parse", "line": 572, "message": "< CLOSE 1000 (OK) [2 bytes]", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9117525, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "discard", "line": 621, "message": "< EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.912557, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_eof", "line": 731, "message": "> EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.913283, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSED", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9143863, "level": "DEBUG", "logger": "websockets.client", "module": "connection", "function": "send_data", "line": 900, "message": "x closing TCP connection", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9160438, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.917941, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9188595, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.4356818, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_LVNjQunnPB8J", "dispatch_id": "", "room_name": "playground-E3sa-zcO6", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.70451, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.750766, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 163, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7719142, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7731442, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7719142, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7731442, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7843072, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.0831664, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.084273, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.0860922, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.087673, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.3562546, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.3588114, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.3601918, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.3612278, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.6181223, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.6895833, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 163, "elapsed_time": 0.94, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.689595, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017359.2033644, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017359.4615223, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.3994331, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-ZxdR", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.4049742, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-ZxdR", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.4063594, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.4072473, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.408504, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f88fce3aa80>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.408504, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f88fce3aa80>", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.4324586, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.496341, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5010252, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.503032, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5051324, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5066173, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: qoF/dT6WYzJf8PQtvqAuHQ==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5087912, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.510338, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5115104, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5128157, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5140436, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5160644, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.517307, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6585236, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6607323, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.662758, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.666237, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.666237, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6674192, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6689336, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1388:livekit::room - disconnected from room with reason: ClientInitiated", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6674192, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.669303, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6709101, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-ZxdR", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.669303, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.673895, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.675187, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6762915, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
