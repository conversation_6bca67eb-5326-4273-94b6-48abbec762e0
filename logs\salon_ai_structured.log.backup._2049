 
{"timestamp": 1754989383.5608892, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 28, "message": "Test logging setup started", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989383.5608892, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "success", "duration_ms": 21, "user_id": "test_user_52", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989384.0625365, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "startup", "component": "database", "status": "error", "error_type": "TestError", "error_code": 8325, "retry_count": 3, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989384.5663307, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in voice_agent", "taskName": null, "event": "shutdown", "component": "voice_agent", "status": "in_progress", "duration_ms": 214, "user_id": "test_user_2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989385.0675225, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in livekit", "taskName": null, "event": "user_action", "component": "livekit", "status": "success", "duration_ms": 398, "user_id": "test_user_10", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989385.5688086, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in livekit", "taskName": null, "event": "user_action", "component": "livekit", "status": "in_progress", "duration_ms": 605, "user_id": "test_user_82", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989386.0700972, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "warning", "threshold_value": 56, "current_value": 92, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989386.5732875, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 4909, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989387.0766184, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "success", "duration_ms": 397, "user_id": "test_user_22", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989387.5779452, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "success", "duration_ms": 893, "user_id": "test_user_37", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989388.0790868, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in metrics", "taskName": null, "event": "startup", "component": "metrics", "status": "in_progress", "duration_ms": 397, "user_id": "test_user_27", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989388.5815506, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "startup", "component": "metrics", "status": "warning", "threshold_value": 58, "current_value": 100, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989389.0829287, "level": "WARNING", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in database", "taskName": null, "event": "error_recovery", "component": "database", "status": "warning", "threshold_value": 86, "current_value": 119, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989389.5836198, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test error_recovery in metrics", "taskName": null, "event": "error_recovery", "component": "metrics", "status": "success", "duration_ms": 623, "user_id": "test_user_1", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989390.0852108, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "success", "duration_ms": 114, "user_id": "test_user_49", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989390.5869725, "level": "WARNING", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in livekit", "taskName": null, "event": "error_recovery", "component": "livekit", "status": "warning", "threshold_value": 66, "current_value": 95, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989391.0888717, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in livekit", "taskName": null, "event": "user_action", "component": "livekit", "status": "in_progress", "duration_ms": 874, "user_id": "test_user_54", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989391.5900602, "level": "ERROR", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in metrics", "taskName": null, "event": "user_action", "component": "metrics", "status": "error", "error_type": "TestError", "error_code": 8462, "retry_count": 3, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989392.090961, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "user_action", "component": "database", "status": "error", "error_type": "TestError", "error_code": 1132, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989392.592787, "level": "ERROR", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in metrics", "taskName": null, "event": "user_action", "component": "metrics", "status": "error", "error_type": "TestError", "error_code": 8303, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.0941503, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 3861, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.596887, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 83, "message": "Application startup initiated", "taskName": null, "event": "application_startup", "component": "main", "status": "in_progress", "version": "1.0.0", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.596887, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 90, "message": "Database connection established", "taskName": null, "event": "database_connection", "component": "database", "status": "success", "connection_pool_size": 10, "connection_timeout": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.596887, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 98, "message": "Voice agent initialized", "taskName": null, "event": "voice_agent_init", "component": "voice_agent", "status": "success", "model": "gpt-4", "voice_provider": "elevenlabs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.596887, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_3832", "phone_number": "+12709179735", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.6015878, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_3832", "phone_number": "+12709179735", "call_duration": 461, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.6015878, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_1051", "phone_number": "+17690644449", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989395.603675, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_1051", "phone_number": "+17690644449", "call_duration": 106, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989395.603675, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_2003", "phone_number": "+19801653391", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989396.605023, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_2003", "phone_number": "+19801653391", "call_duration": 438, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989396.605023, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_7107", "phone_number": "+13919187337", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989397.6083195, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_7107", "phone_number": "+13919187337", "call_duration": 478, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1754989397.6083195, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_3750", "phone_number": "+15243094178", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.6110148, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_3750", "phone_number": "+15243094178", "call_duration": 457, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.6110148, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 133, "message": "Database connection lost", "taskName": null, "event": "database_error", "component": "database", "status": "error", "error_type": "ConnectionError", "error_message": "Connection timeout after 30 seconds", "retry_attempt": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.6110148, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 142, "message": "Voice synthesis failed", "taskName": null, "event": "voice_synthesis_error", "component": "voice_agent", "status": "error", "error_type": "APIError", "provider": "elevenlabs", "error_code": 429, "rate_limit_exceeded": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.6110148, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 153, "message": "Performance metrics collected", "taskName": null, "event": "metrics_collection", "component": "metrics", "status": "success", "cpu_usage": 75, "memory_usage": 57, "active_calls": 7, "response_time_ms": 103, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.614447, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 163, "message": "Test logging completed", "taskName": null, "event": "test_completed", "component": "test", "status": "success", "total_logs_generated": 30, "test_duration": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4627817, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 28, "message": "Test logging setup started", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4652045, "level": "WARNING", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in voice_agent", "taskName": null, "event": "shutdown", "component": "voice_agent", "status": "warning", "threshold_value": 70, "current_value": 95, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.9670885, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "error_recovery", "component": "database", "status": "error", "error_type": "TestError", "error_code": 3140, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008944.4679577, "level": "ERROR", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in livekit", "taskName": null, "event": "user_action", "component": "livekit", "status": "error", "error_type": "TestError", "error_code": 6264, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008944.969077, "level": "WARNING", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in livekit", "taskName": null, "event": "startup", "component": "livekit", "status": "warning", "threshold_value": 80, "current_value": 83, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008945.4700162, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "startup", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 9663, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008945.9707935, "level": "WARNING", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "warning", "threshold_value": 86, "current_value": 83, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008946.4714594, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test error_recovery in livekit", "taskName": null, "event": "error_recovery", "component": "livekit", "status": "in_progress", "duration_ms": 712, "user_id": "test_user_99", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008946.9722672, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test error_recovery in livekit", "taskName": null, "event": "error_recovery", "component": "livekit", "status": "success", "duration_ms": 332, "user_id": "test_user_40", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008947.4730077, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "success", "duration_ms": 928, "user_id": "test_user_39", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008947.974357, "level": "ERROR", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "error", "error_type": "TestError", "error_code": 8269, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008948.4754658, "level": "WARNING", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in voice_agent", "taskName": null, "event": "startup", "component": "voice_agent", "status": "warning", "threshold_value": 95, "current_value": 118, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008948.9760137, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in livekit", "taskName": null, "event": "user_action", "component": "livekit", "status": "success", "duration_ms": 433, "user_id": "test_user_12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008949.4769619, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "success", "duration_ms": 252, "user_id": "test_user_51", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008949.9779515, "level": "WARNING", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in database", "taskName": null, "event": "user_action", "component": "database", "status": "warning", "threshold_value": 93, "current_value": 89, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008950.478794, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in livekit", "taskName": null, "event": "user_action", "component": "livekit", "status": "success", "duration_ms": 382, "user_id": "test_user_100", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008950.9794822, "level": "WARNING", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in livekit", "taskName": null, "event": "performance_check", "component": "livekit", "status": "warning", "threshold_value": 97, "current_value": 115, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008951.4802792, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "in_progress", "duration_ms": 552, "user_id": "test_user_9", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008951.982469, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "warning", "threshold_value": 87, "current_value": 95, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008952.483642, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in voice_agent", "taskName": null, "event": "startup", "component": "voice_agent", "status": "success", "duration_ms": 625, "user_id": "test_user_79", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008952.9848948, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "success", "duration_ms": 144, "user_id": "test_user_44", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4857304, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 83, "message": "Application startup initiated", "taskName": null, "event": "application_startup", "component": "main", "status": "in_progress", "version": "1.0.0", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4857304, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 90, "message": "Database connection established", "taskName": null, "event": "database_connection", "component": "database", "status": "success", "connection_pool_size": 10, "connection_timeout": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4857304, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 98, "message": "Voice agent initialized", "taskName": null, "event": "voice_agent_init", "component": "voice_agent", "status": "success", "model": "gpt-4", "voice_provider": "elevenlabs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4857304, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_2805", "phone_number": "+19560454381", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4877799, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_2805", "phone_number": "+19560454381", "call_duration": 78, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4877799, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_4313", "phone_number": "+11661724430", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008955.4886606, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_4313", "phone_number": "+11661724430", "call_duration": 190, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008955.4886606, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_1346", "phone_number": "+13671462151", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008956.4899542, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_1346", "phone_number": "+13671462151", "call_duration": 107, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008956.4899542, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_6395", "phone_number": "+15608741449", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008957.49114, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_6395", "phone_number": "+15608741449", "call_duration": 266, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755008957.49114, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_4602", "phone_number": "+11323051428", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4923854, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_4602", "phone_number": "+11323051428", "call_duration": 164, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4923854, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 133, "message": "Database connection lost", "taskName": null, "event": "database_error", "component": "database", "status": "error", "error_type": "ConnectionError", "error_message": "Connection timeout after 30 seconds", "retry_attempt": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4923854, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 142, "message": "Voice synthesis failed", "taskName": null, "event": "voice_synthesis_error", "component": "voice_agent", "status": "error", "error_type": "APIError", "provider": "elevenlabs", "error_code": 429, "rate_limit_exceeded": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4923854, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 153, "message": "Performance metrics collected", "taskName": null, "event": "metrics_collection", "component": "metrics", "status": "success", "cpu_usage": 61, "memory_usage": 47, "active_calls": 2, "response_time_ms": 370, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4923854, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 163, "message": "Test logging completed", "taskName": null, "event": "test_completed", "component": "test", "status": "success", "total_logs_generated": 30, "test_duration": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.922406, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.9333284, "level": "DEV", "logger": "livekit.agents", "module": "watcher", "function": "run", "line": 84, "message": "Watching D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent", "taskName": "Task-1", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009201.373088, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009201.3851597, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009201.3898218, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 65, "message": "\u001b[1msee tracing information at http://localhost:53640/debug\u001b[0m", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009202.0604436, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_FyMxQyw4HHMe", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "India", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009242.932284, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009242.943762, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009242.9477577, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 65, "message": "\u001b[1msee tracing information at http://localhost:53670/debug\u001b[0m", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009242.9467573, "level": "ERROR", "logger": "livekit.agents", "module": "log", "function": "async_fn_logs", "line": 21, "message": "Error in _read_ipc_task", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\utils\\aio\\duplex_unix.py\", line 35, in recv_bytes\n    len_bytes = await self._reader.readexactly(4)\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\streams.py\", line 750, in readexactly\n    raise exceptions.IncompleteReadError(incomplete, n)\nasyncio.exceptions.IncompleteReadError: 0 bytes read on a total of 4 expected bytes\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\utils\\log.py\", line 16, in async_fn_logs\n    return await fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\cli\\watcher.py\", line 120, in _read_ipc_task\n    msg = await channel.arecv_message(self._pch, proto.IPC_MESSAGES)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\ipc\\channel.py\", line 47, in arecv_message\n    return _read_message(await dplx.recv_bytes(), messages)\n                         ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\utils\\aio\\duplex_unix.py\", line 43, in recv_bytes\n    raise DuplexClosed() from e\nlivekit.agents.utils.aio.duplex_unix.DuplexClosed", "taskName": "Task-2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009243.417445, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_LVd42EcxqFVs", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "India", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009308.8823009, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 706, "message": "failed to connect to livekit, retrying in 0s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 693, in _connection_task\n    await self._run_ws(ws)\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 774, in _run_ws\n    await asyncio.gather(*tasks)\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 746, in _recv_task\n    raise Exception(\"worker connection closed unexpectedly\")\nException: worker connection closed unexpectedly", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009312.3282514, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_Cj9LSi5wBEQN", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "India", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009351.4036617, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009351.408221, "level": "DEV", "logger": "livekit.agents", "module": "watcher", "function": "run", "line": 84, "message": "Watching D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent", "taskName": "Task-1", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009353.9222035, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009353.9297297, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009353.9317281, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 65, "message": "\u001b[1msee tracing information at http://localhost:51846/debug\u001b[0m", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009354.1741402, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_k96HoJkKKWdo", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "India", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009354.2194765, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-13", "job_id": "AJ_dJJPgGQJ282B", "dispatch_id": "", "room_name": "playground-F2ng-Z364", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009354.259752, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 164, "message": "initializing job runner", "taskName": "Task-14", "tid": 14276, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009354.260744, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 173, "message": "job runner initialized", "taskName": "Task-14", "tid": 14276, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009354.260744, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009354.263772, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009354.9882731, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009354.989273, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 36, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009360.5746033, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x0000022B5001CBF0>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009360.5761166, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task finished name='job_user_entrypoint' coro=<entrypoint() done, defined at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\main.py:80> result=None> took too long: 2.22 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009360.5761166, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task finished name='job_user_entrypoint' coro=<entrypoint() done, defined at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\main.py:80> result=None> took too long: 2.22 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009360.5791638, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009362.543652, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\voice\\room_io\\_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\voice\\room_io\\_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-32", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009362.5446584, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"2384990644048\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"D:\\\\\\\\Work\\\\\\\\ElectronikMedia\\\\\\\\Salon AI\\\\\\\\voice-agent\\\\\\\\.venv\\\\\\\\Lib\\\\\\\\site-packages\\\\\\\\livekit\\\\\\\\plugins\\\\\\\\noise_cancellation\\\\\\\\resources\\\\\\\\inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009362.6175523, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009362.6195607, "level": "DEBUG", "logger": "livekit.agents", "module": "job_thread_executor", "function": "_monitor_task", "line": 291, "message": "job exiting", "taskName": "Task-20", "reason": "", "tid": 14276, "job_id": "AJ_dJJPgGQJ282B", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009362.6214056, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-49' coro=<AudioStream._run() done, defined at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009366.9148393, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task pending name='job_shutdown_callback' coro=<JobContext.add_shutdown_callback.<locals>.wrapper() running at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\job.py:232> wait_for=<Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\futures.py:389, Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\tasks.py:767]> took too long: 4.28 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009366.9148393, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task pending name='job_shutdown_callback' coro=<JobContext.add_shutdown_callback.<locals>.wrapper() running at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\job.py:232> wait_for=<Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\futures.py:389, Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\tasks.py:767]> took too long: 4.28 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009367.6259882, "level": "DEBUG", "logger": "tortoise.db_client", "module": "client", "function": "create_connection", "line": 62, "message": "Created connection pool <asyncpg.pool.Pool object at 0x0000022B4C8F4AC0> with params: {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'database': 'postgres', 'min_size': 1, 'max_size': 10, 'connection_class': <class 'asyncpg.connection.Connection'>, 'loop': None, 'server_settings': {}, 'command_timeout': 60}", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009367.6270564, "level": "DEBUG", "logger": "tortoise.db_client", "module": "client", "function": "execute_insert", "line": 110, "message": "INSERT INTO \"recordings\" (\"id\",\"file_path\",\"purpose\",\"summary\",\"transcript\",\"duration_minutes\",\"call_quality_score\",\"created_at\",\"updated_at\",\"appointment_id\",\"customer_id\",\"room_id\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12): ['6ba8bae1-a1e6-4723-a675-33d858bdb8b0', 'recordings/playground-F2ng-Z364/recording.ogg', 'TESTING PURPOSE', 'It appears that no content has been provided for me to summarize. Please share the transcription of a customer service conversation from Glamour Salon, and I will generate a structured summary based on the guidelines provided.', '{\"items\":[]}', Decimal('0.07'), None, datetime.datetime(2025, 8, 12, 14, 36, 6, 912914, tzinfo=<UTC>), datetime.datetime(2025, 8, 12, 14, 36, 6, 912914, tzinfo=<UTC>), None, '2de9ed22-ded8-4afa-a381-0f2d5affd17a', None]", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009367.6640015, "level": "INFO", "logger": "google_genai.live", "module": "live", "function": "_connect", "line": 1051, "message": "b'{\\n  \"setupComplete\": {}\\n}\\n'", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009367.8040028, "level": "DEBUG", "logger": "tortoise.db_client", "module": "client", "function": "_close", "line": 85, "message": "Closed connection pool None with params: {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'database': 'postgres', 'min_size': 1, 'max_size': 10, 'connection_class': <class 'asyncpg.connection.Connection'>, 'loop': None, 'server_settings': {}, 'command_timeout': 60}", "taskName": "Task-56", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009367.8050048, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009367.8050048, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009367.806082, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 55, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009367.806082, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009367.806082, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009407.695861, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009407.7015028, "level": "DEV", "logger": "livekit.agents", "module": "watcher", "function": "run", "line": 84, "message": "Watching D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent", "taskName": "Task-1", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009409.9269264, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009409.9461107, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009409.9481652, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 65, "message": "\u001b[1msee tracing information at http://localhost:52722/debug\u001b[0m", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009411.0790431, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_cRKri8dy8yQE", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009419.0067508, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-14", "job_id": "AJ_NNA3ispvZBaT", "dispatch_id": "", "room_name": "playground-DNlC-U0ku", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009419.321689, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 164, "message": "initializing job runner", "taskName": "Task-15", "tid": 40172, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009419.321689, "level": "DEBUG", "logger": "asyncio", "module": "proactor_events", "function": "__init__", "line": 634, "message": "Using proactor: IocpProactor", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009419.321689, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 173, "message": "job runner initialized", "taskName": "Task-15", "tid": 40172, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009419.321689, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009419.3767893, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009419.3782277, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 36, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009428.0434375, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x00000199091FC7D0>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009428.04547, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009429.4984546, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\voice\\room_io\\_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\voice\\room_io\\_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-34", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009429.4984546, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"1756736333648\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"D:\\\\\\\\Work\\\\\\\\ElectronikMedia\\\\\\\\Salon AI\\\\\\\\voice-agent\\\\\\\\.venv\\\\\\\\Lib\\\\\\\\site-packages\\\\\\\\livekit\\\\\\\\plugins\\\\\\\\noise_cancellation\\\\\\\\resources\\\\\\\\inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009429.7549057, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009429.756941, "level": "DEBUG", "logger": "livekit.agents", "module": "job_thread_executor", "function": "_monitor_task", "line": 291, "message": "job exiting", "taskName": "Task-24", "reason": "", "tid": 40172, "job_id": "AJ_NNA3ispvZBaT", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009429.7580583, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-51' coro=<AudioStream._run() done, defined at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\rtc\\audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009432.3837533, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task pending name='job_shutdown_callback' coro=<JobContext.add_shutdown_callback.<locals>.wrapper() running at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\job.py:232> wait_for=<Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\futures.py:389, Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\tasks.py:767]> took too long: 2.62 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009432.3837533, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task pending name='job_shutdown_callback' coro=<JobContext.add_shutdown_callback.<locals>.wrapper() running at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\job.py:232> wait_for=<Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\futures.py:389, Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\tasks.py:767]> took too long: 2.62 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009432.7938747, "level": "INFO", "logger": "google_genai.live", "module": "live", "function": "_connect", "line": 1051, "message": "b'{\\n  \"setupComplete\": {}\\n}\\n'", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009433.0166783, "level": "DEBUG", "logger": "tortoise.db_client", "module": "client", "function": "create_connection", "line": 62, "message": "Created connection pool <asyncpg.pool.Pool object at 0x00000199095C3640> with params: {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'database': 'postgres', 'min_size': 1, 'max_size': 10, 'connection_class': <class 'asyncpg.connection.Connection'>, 'loop': None, 'server_settings': {}, 'command_timeout': 60}", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009433.017191, "level": "DEBUG", "logger": "tortoise.db_client", "module": "client", "function": "execute_insert", "line": 110, "message": "INSERT INTO \"recordings\" (\"id\",\"file_path\",\"purpose\",\"summary\",\"transcript\",\"duration_minutes\",\"call_quality_score\",\"created_at\",\"updated_at\",\"appointment_id\",\"customer_id\",\"room_id\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12): ['29755362-207e-4cf8-a8b5-1e52ffa54e24', 'recordings/playground-DNlC-U0ku/recording.ogg', 'TESTING PURPOSE', 'It seems there was no transcription provided for analysis. Please provide a transcription of a customer service conversation from Glamour Salon for me to summarize.', '{\"items\":[]}', Decimal('0.06'), None, datetime.datetime(2025, 8, 12, 14, 37, 12, 381781, tzinfo=<UTC>), datetime.datetime(2025, 8, 12, 14, 37, 12, 381781, tzinfo=<UTC>), None, '2de9ed22-ded8-4afa-a381-0f2d5affd17a', None]", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009433.1958797, "level": "DEBUG", "logger": "tortoise.db_client", "module": "client", "function": "_close", "line": 85, "message": "Closed connection pool None with params: {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'database': 'postgres', 'min_size': 1, 'max_size': 10, 'connection_class': <class 'asyncpg.connection.Connection'>, 'loop': None, 'server_settings': {}, 'command_timeout': 60}", "taskName": "Task-58", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009433.1973941, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009433.1973941, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009433.1973941, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 55, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009433.1984065, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009433.1984065, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009816.648529, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009816.6508164, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.862652, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.8652806, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.8694217, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.8764524, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.8818793, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.884772, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.889855, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.8957117, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.902095, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.9051962, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.9202754, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009818.9246674, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.2678764, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 67, "elapsed_time": 2.38, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.2706811, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.2702384, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 59, "elapsed_time": 2.4, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.2686706, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.2684915, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.2737372, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 69, "elapsed_time": 2.38, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.304363, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 61, "elapsed_time": 2.43, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.3044124, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.3085465, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.309143, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 63, "elapsed_time": 2.43, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.336884, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 2.47, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.3385808, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 65, "elapsed_time": 2.45, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.3383718, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.3374572, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.3380375, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.3399668, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 71, "elapsed_time": 2.44, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.3555796, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 73, "elapsed_time": 2.45, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.3555753, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.419445, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 75, "elapsed_time": 2.51, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.4193852, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.4203115, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.4217603, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 77, "elapsed_time": 2.5, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.5076919, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 78, "elapsed_time": 2.58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.5077882, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009821.5098975, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009822.7665598, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_YtoqBJzAcoK2", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009930.8673677, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-143", "job_id": "AJ_cfPZY6HWqZZR", "dispatch_id": "", "room_name": "playground-rhht-eKOo", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009931.1768613, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009931.2587128, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009931.260042, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 36, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009931.2587128, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "pid": 67, "job_id": "AJ_cfPZY6HWqZZR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009931.260042, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 36, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "pid": 67, "job_id": "AJ_cfPZY6HWqZZR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009931.2812862, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-145", "pid": 251, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009932.2273364, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-145", "pid": 251, "elapsed_time": 0.95, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009932.2272835, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009932.5251312, "level": "ERROR", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "log_exception", "line": 278, "message": "unhandled exception while running the job task", "exc_info": "Traceback (most recent call last):\n  File \"/app/main.py\", line 178, in entrypoint\n    raise e\n  File \"/app/main.py\", line 105, in entrypoint\n    await recording_and_transcript.make_egress_req()\n  File \"/app/livekit_handlers/recording.py\", line 65, in make_egress_req\n    await lkapi.egress.start_room_composite_egress(room_req)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/api/egress_service.py\", line 41, in start_room_composite_egress\n    return await self._client.request(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/api/twirp_client.py\", line 129, in request\n    raise TwirpError(\nlivekit.api.twirp_client.TwirpError: TwirpError(code=resource_exhausted, message=concurrent egress sessions limit exceeded, status=429)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755009932.5251312, "level": "ERROR", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "log_exception", "line": 278, "message": "unhandled exception while running the job task\nTraceback (most recent call last):\n  File \"/app/main.py\", line 178, in entrypoint\n    raise e\n  File \"/app/main.py\", line 105, in entrypoint\n    await recording_and_transcript.make_egress_req()\n  File \"/app/livekit_handlers/recording.py\", line 65, in make_egress_req\n    await lkapi.egress.start_room_composite_egress(room_req)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/api/egress_service.py\", line 41, in start_room_composite_egress\n    return await self._client.request(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/api/twirp_client.py\", line 129, in request\n    raise TwirpError(\nlivekit.api.twirp_client.TwirpError: TwirpError(code=resource_exhausted, message=concurrent egress sessions limit exceeded, status=429)", "taskName": null, "pid": 67, "job_id": "AJ_cfPZY6HWqZZR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010026.3983893, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-156", "id": "AW_YtoqBJzAcoK2", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010056.283637, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010056.284478, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.5318904, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.536575, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.540963, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.5455372, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.550022, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.5573914, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.5613225, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.5671928, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.5790932, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.5896895, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.6010935, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010058.6081192, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.3504193, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 75, "elapsed_time": 1.76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.3498943, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.3579721, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.362586, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 65, "elapsed_time": 1.81, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.3899827, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 67, "elapsed_time": 1.83, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.3900254, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.5670218, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.567008, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 61, "elapsed_time": 2.02, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.5727642, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 63, "elapsed_time": 2.03, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.572737, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.5727527, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.5747569, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 77, "elapsed_time": 1.97, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6144657, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 71, "elapsed_time": 2.05, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6145368, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6199234, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 69, "elapsed_time": 2.06, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6198387, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6384408, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 59, "elapsed_time": 2.1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6384354, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6394932, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6396477, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6403005, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 73, "elapsed_time": 2.06, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.6412592, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 2.11, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.7372296, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 79, "elapsed_time": 2.13, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.7372422, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010060.739528, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010063.4562867, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_k55F6nGBMAQH", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010082.6165688, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_k55F6nGBMAQH", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010082.6181138, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_k55F6nGBMAQH", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010260.6372995, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010260.6385763, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.6951275, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.697341, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.7002945, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.7027397, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.7074103, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.7118526, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.715839, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.7187057, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.7213995, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.7252681, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.731237, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010262.735018, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.9610407, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.9610386, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.9713295, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.9687707, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 63, "elapsed_time": 2.26, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.97843, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.9836495, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 75, "elapsed_time": 2.26, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.982819, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.9916606, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.9945135, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 59, "elapsed_time": 2.3, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.995767, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.9976735, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 2.3, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010264.999384, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 65, "elapsed_time": 2.29, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.006996, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.0122101, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 71, "elapsed_time": 2.29, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.013587, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 73, "elapsed_time": 2.29, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.0175827, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 67, "elapsed_time": 2.3, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.1102114, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.1101842, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 61, "elapsed_time": 2.41, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.1115623, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.116326, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 69, "elapsed_time": 2.4, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.1102571, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.117852, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 79, "elapsed_time": 2.38, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.235878, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 77, "elapsed_time": 2.5, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.236004, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010265.241822, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010266.3971038, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_FMMuotunKEpt", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010608.4938629, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_FMMuotunKEpt", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010608.4962275, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_FMMuotunKEpt", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010633.0265136, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010633.028016, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.44134, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.444596, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.4482465, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.4515486, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.4550686, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.462898, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.4658816, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.4703772, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.481551, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.4857526, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.4901729, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010635.502684, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.046002, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.0459893, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 61, "elapsed_time": 1.6, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.045301, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.050023, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 69, "elapsed_time": 1.58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.102189, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 1.66, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1021104, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1022224, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.104262, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 65, "elapsed_time": 1.65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1247964, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 67, "elapsed_time": 1.66, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1247954, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.143771, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 73, "elapsed_time": 1.66, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1437316, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1455748, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 71, "elapsed_time": 1.67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1456316, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1569493, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 63, "elapsed_time": 1.7, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1568732, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1971502, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 77, "elapsed_time": 1.7, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1973758, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1971247, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.1993663, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 59, "elapsed_time": 1.75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.2431254, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 79, "elapsed_time": 1.74, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.2431178, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.2688558, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 75, "elapsed_time": 1.78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.2687345, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.2713208, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010637.8782313, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_PqreB54yMCzY", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "India", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010838.797419, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 28, "message": "Test logging setup started", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010838.797419, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "startup", "component": "database", "status": "error", "error_type": "TestError", "error_code": 9979, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010839.2991352, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in livekit", "taskName": null, "event": "startup", "component": "livekit", "status": "success", "duration_ms": 667, "user_id": "test_user_28", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010839.799745, "level": "WARNING", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in database", "taskName": null, "event": "error_recovery", "component": "database", "status": "warning", "threshold_value": 70, "current_value": 108, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010840.3006604, "level": "ERROR", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "error", "error_type": "TestError", "error_code": 8842, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010840.801209, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 3407, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010841.3019097, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "success", "duration_ms": 612, "user_id": "test_user_83", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010841.802837, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "in_progress", "duration_ms": 684, "user_id": "test_user_35", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010842.3036754, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in voice_agent", "taskName": null, "event": "startup", "component": "voice_agent", "status": "in_progress", "duration_ms": 565, "user_id": "test_user_40", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010842.80452, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test error_recovery in database", "taskName": null, "event": "error_recovery", "component": "database", "status": "in_progress", "duration_ms": 880, "user_id": "test_user_44", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010843.3053284, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test error_recovery in voice_agent", "taskName": null, "event": "error_recovery", "component": "voice_agent", "status": "success", "duration_ms": 814, "user_id": "test_user_1", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010843.8060603, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "user_action", "component": "database", "status": "error", "error_type": "TestError", "error_code": 5418, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010844.3067653, "level": "WARNING", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "warning", "threshold_value": 71, "current_value": 80, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010844.8076782, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test error_recovery in metrics", "taskName": null, "event": "error_recovery", "component": "metrics", "status": "in_progress", "duration_ms": 224, "user_id": "test_user_23", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010845.3085136, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "warning", "threshold_value": 82, "current_value": 83, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010845.8091934, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "warning", "threshold_value": 57, "current_value": 108, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010846.3098967, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test error_recovery in database", "taskName": null, "event": "error_recovery", "component": "database", "status": "success", "duration_ms": 130, "user_id": "test_user_79", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010846.810651, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "error_recovery", "component": "database", "status": "error", "error_type": "TestError", "error_code": 8515, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010847.3112414, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 7561, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010847.8119926, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 8401, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3130746, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in metrics", "taskName": null, "event": "startup", "component": "metrics", "status": "in_progress", "duration_ms": 409, "user_id": "test_user_77", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8136485, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 83, "message": "Application startup initiated", "taskName": null, "event": "application_startup", "component": "main", "status": "in_progress", "version": "1.0.0", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.814176, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 90, "message": "Database connection established", "taskName": null, "event": "database_connection", "component": "database", "status": "success", "connection_pool_size": 10, "connection_timeout": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.814176, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 98, "message": "Voice agent initialized", "taskName": null, "event": "voice_agent_init", "component": "voice_agent", "status": "success", "model": "gpt-4", "voice_provider": "elevenlabs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8147173, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_2273", "phone_number": "+14958774840", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8160388, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_2273", "phone_number": "+14958774840", "call_duration": 584, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8160388, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_3980", "phone_number": "+17853294275", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010850.8171391, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_3980", "phone_number": "+17853294275", "call_duration": 270, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010850.8176029, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_9931", "phone_number": "+17560269316", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010851.818307, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_9931", "phone_number": "+17560269316", "call_duration": 222, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010851.818307, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_1330", "phone_number": "+11952262146", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010852.819295, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_1330", "phone_number": "+11952262146", "call_duration": 374, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755010852.8198166, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_1709", "phone_number": "+14827377056", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.820142, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_1709", "phone_number": "+14827377056", "call_duration": 492, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8209302, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 133, "message": "Database connection lost", "taskName": null, "event": "database_error", "component": "database", "status": "error", "error_type": "ConnectionError", "error_message": "Connection timeout after 30 seconds", "retry_attempt": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8209302, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 142, "message": "Voice synthesis failed", "taskName": null, "event": "voice_synthesis_error", "component": "voice_agent", "status": "error", "error_type": "APIError", "provider": "elevenlabs", "error_code": 429, "rate_limit_exceeded": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8218772, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 153, "message": "Performance metrics collected", "taskName": null, "event": "metrics_collection", "component": "metrics", "status": "success", "cpu_usage": 33, "memory_usage": 57, "active_calls": 8, "response_time_ms": 106, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8218772, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 163, "message": "Test logging completed", "taskName": null, "event": "test_completed", "component": "test", "status": "success", "total_logs_generated": 30, "test_duration": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.6136253, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-142", "id": "AW_PqreB54yMCzY", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.648163, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-143", "id": "AW_PqreB54yMCzY", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011226.272809, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Future exception was never retrieved\nfuture: <Future finished exception=ClientConnectionError('Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)')>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/asyncio/sslproto.py\", line 651, in _do_shutdown\n    self._sslobj.unwrap()\n  File \"/usr/local/lib/python3.12/ssl.py\", line 920, in unwrap\n    return self._sslobj.shutdown()\n           ^^^^^^^^^^^^^^^^^^^^^^^\nssl.SSLError: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)\n\nThe above exception was the direct cause of the following exception:\n\naiohttp.client_exceptions.ClientConnectionError: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011233.2009492, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011233.2020755, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7202344, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7235522, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7288737, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7356668, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7408195, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7463496, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7532809, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7609704, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7684941, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.780453, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.7885242, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011235.8000965, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3616638, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 69, "elapsed_time": 1.61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3617804, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3625743, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3644447, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 63, "elapsed_time": 1.63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3910136, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 75, "elapsed_time": 1.61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3911955, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3916533, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3911211, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.392623, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 59, "elapsed_time": 1.67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3938842, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 1.67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.397747, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.3993905, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 71, "elapsed_time": 1.64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.4535286, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 79, "elapsed_time": 1.65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.4536264, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.4544544, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.4544966, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.4552934, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 65, "elapsed_time": 1.71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.454957, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.4565384, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 73, "elapsed_time": 1.69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.458153, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 61, "elapsed_time": 1.73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.511982, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 67, "elapsed_time": 1.76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.5119684, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.5122018, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.5134144, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 77, "elapsed_time": 1.72, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011237.5159795, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011238.67447, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_XSQDwAZk4ZKu", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011299.3187425, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 28, "message": "Test logging setup started", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011299.3187425, "level": "ERROR", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in livekit", "taskName": null, "event": "error_recovery", "component": "livekit", "status": "error", "error_type": "TestError", "error_code": 2167, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011299.8230762, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "error_recovery", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 7141, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011300.324371, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "success", "duration_ms": 230, "user_id": "test_user_18", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011300.8253222, "level": "ERROR", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "error", "error_type": "TestError", "error_code": 1507, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011301.3264215, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in metrics", "taskName": null, "event": "user_action", "component": "metrics", "status": "in_progress", "duration_ms": 993, "user_id": "test_user_41", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011301.8273134, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "warning", "threshold_value": 90, "current_value": 92, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011302.3281975, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 3102, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011302.8297014, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in database", "taskName": null, "event": "performance_check", "component": "database", "status": "success", "duration_ms": 142, "user_id": "test_user_84", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011303.330617, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "shutdown", "component": "database", "status": "error", "error_type": "TestError", "error_code": 1806, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011303.831807, "level": "ERROR", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in livekit", "taskName": null, "event": "error_recovery", "component": "livekit", "status": "error", "error_type": "TestError", "error_code": 1564, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011304.3331974, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in metrics", "taskName": null, "event": "startup", "component": "metrics", "status": "in_progress", "duration_ms": 942, "user_id": "test_user_21", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011304.8349833, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in metrics", "taskName": null, "event": "user_action", "component": "metrics", "status": "success", "duration_ms": 59, "user_id": "test_user_27", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011305.3367329, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test error_recovery in voice_agent", "taskName": null, "event": "error_recovery", "component": "voice_agent", "status": "success", "duration_ms": 360, "user_id": "test_user_15", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011305.8384504, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in database", "taskName": null, "event": "startup", "component": "database", "status": "success", "duration_ms": 986, "user_id": "test_user_44", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011306.340986, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in database", "taskName": null, "event": "user_action", "component": "database", "status": "in_progress", "duration_ms": 743, "user_id": "test_user_73", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011306.8419957, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "error_recovery", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 5777, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011307.3435342, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "user_action", "component": "metrics", "status": "warning", "threshold_value": 90, "current_value": 120, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011307.8447506, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in voice_agent", "taskName": null, "event": "shutdown", "component": "voice_agent", "status": "success", "duration_ms": 191, "user_id": "test_user_88", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011308.345582, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "startup", "component": "database", "status": "error", "error_type": "TestError", "error_code": 3517, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011308.846128, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "startup", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 1649, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3475983, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 83, "message": "Application startup initiated", "taskName": null, "event": "application_startup", "component": "main", "status": "in_progress", "version": "1.0.0", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3475983, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 90, "message": "Database connection established", "taskName": null, "event": "database_connection", "component": "database", "status": "success", "connection_pool_size": 10, "connection_timeout": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3475983, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 98, "message": "Voice agent initialized", "taskName": null, "event": "voice_agent_init", "component": "voice_agent", "status": "success", "model": "gpt-4", "voice_provider": "elevenlabs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3475983, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_9908", "phone_number": "+14102114739", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3513887, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_9908", "phone_number": "+14102114739", "call_duration": 588, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3513887, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_6812", "phone_number": "+13727924929", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011311.3536146, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_6812", "phone_number": "+13727924929", "call_duration": 129, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011311.3536146, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_5012", "phone_number": "+11796003108", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011312.3551722, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_5012", "phone_number": "+11796003108", "call_duration": 103, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011312.3551722, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_3878", "phone_number": "+15299556578", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011313.3562014, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_3878", "phone_number": "+15299556578", "call_duration": 77, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011313.3562014, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_7897", "phone_number": "+19587110017", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3577554, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_7897", "phone_number": "+19587110017", "call_duration": 252, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3577554, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 133, "message": "Database connection lost", "taskName": null, "event": "database_error", "component": "database", "status": "error", "error_type": "ConnectionError", "error_message": "Connection timeout after 30 seconds", "retry_attempt": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.358455, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 142, "message": "Voice synthesis failed", "taskName": null, "event": "voice_synthesis_error", "component": "voice_agent", "status": "error", "error_type": "APIError", "provider": "elevenlabs", "error_code": 429, "rate_limit_exceeded": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.358455, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 153, "message": "Performance metrics collected", "taskName": null, "event": "metrics_collection", "component": "metrics", "status": "success", "cpu_usage": 33, "memory_usage": 72, "active_calls": 4, "response_time_ms": 326, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.358455, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 163, "message": "Test logging completed", "taskName": null, "event": "test_completed", "component": "test", "status": "success", "total_logs_generated": 30, "test_duration": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3615716, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_XSQDwAZk4ZKu", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.36726, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_XSQDwAZk4ZKu", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011517.466502, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011517.4679093, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.600604, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.6027937, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.6047833, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.6080759, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.6101198, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.612867, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.616917, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.6206193, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.6276717, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.6353889, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.6422386, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011519.6443794, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.2330208, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 71, "elapsed_time": 1.61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.2331207, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.2334027, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.2371929, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.2392476, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 67, "elapsed_time": 1.63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.241352, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 73, "elapsed_time": 1.61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.2415595, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.2434273, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 59, "elapsed_time": 1.64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.2540934, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 63, "elapsed_time": 1.64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.2541153, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.342083, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 75, "elapsed_time": 1.7, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.3421187, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.3438392, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 77, "elapsed_time": 1.7, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.3423557, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.4004183, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 61, "elapsed_time": 1.79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.4004653, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.4010274, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.401335, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.4021513, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 57, "elapsed_time": 1.8, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.403421, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 65, "elapsed_time": 1.79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.4324703, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 78, "elapsed_time": 1.79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.4324303, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.4328952, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.433748, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 69, "elapsed_time": 1.82, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011521.4354925, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011522.498659, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_cJXAobJywMXz", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011583.3730514, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 28, "message": "Test logging setup started", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011583.3741267, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "error_recovery", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 6431, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011583.8746896, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "error_recovery", "component": "database", "status": "error", "error_type": "TestError", "error_code": 5267, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011584.3760571, "level": "WARNING", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in database", "taskName": null, "event": "error_recovery", "component": "database", "status": "warning", "threshold_value": 67, "current_value": 117, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011584.8771107, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in livekit", "taskName": null, "event": "performance_check", "component": "livekit", "status": "in_progress", "duration_ms": 174, "user_id": "test_user_100", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011585.3786895, "level": "ERROR", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in metrics", "taskName": null, "event": "startup", "component": "metrics", "status": "error", "error_type": "TestError", "error_code": 1043, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011585.880378, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in database", "taskName": null, "event": "user_action", "component": "database", "status": "success", "duration_ms": 865, "user_id": "test_user_100", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011586.3819656, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in voice_agent", "taskName": null, "event": "shutdown", "component": "voice_agent", "status": "in_progress", "duration_ms": 948, "user_id": "test_user_69", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011586.8838506, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in voice_agent", "taskName": null, "event": "startup", "component": "voice_agent", "status": "success", "duration_ms": 560, "user_id": "test_user_86", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011587.3854713, "level": "WARNING", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in voice_agent", "taskName": null, "event": "startup", "component": "voice_agent", "status": "warning", "threshold_value": 63, "current_value": 97, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011587.8862913, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in livekit", "taskName": null, "event": "startup", "component": "livekit", "status": "in_progress", "duration_ms": 111, "user_id": "test_user_70", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011588.3871357, "level": "WARNING", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in voice_agent", "taskName": null, "event": "error_recovery", "component": "voice_agent", "status": "warning", "threshold_value": 55, "current_value": 82, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011588.8881402, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "success", "duration_ms": 158, "user_id": "test_user_100", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011589.3889244, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "warning", "threshold_value": 60, "current_value": 82, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011589.889516, "level": "ERROR", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "error", "error_type": "TestError", "error_code": 8771, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011590.3909795, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "in_progress", "duration_ms": 920, "user_id": "test_user_14", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011590.8919365, "level": "WARNING", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in database", "taskName": null, "event": "performance_check", "component": "database", "status": "warning", "threshold_value": 76, "current_value": 86, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011591.3932712, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in database", "taskName": null, "event": "user_action", "component": "database", "status": "in_progress", "duration_ms": 27, "user_id": "test_user_93", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011591.8940556, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in livekit", "taskName": null, "event": "performance_check", "component": "livekit", "status": "in_progress", "duration_ms": 618, "user_id": "test_user_89", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011592.3950386, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test startup in database", "taskName": null, "event": "startup", "component": "database", "status": "in_progress", "duration_ms": 497, "user_id": "test_user_82", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011592.8957663, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "success", "duration_ms": 413, "user_id": "test_user_36", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3971367, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 83, "message": "Application startup initiated", "taskName": null, "event": "application_startup", "component": "main", "status": "in_progress", "version": "1.0.0", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3976645, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 90, "message": "Database connection established", "taskName": null, "event": "database_connection", "component": "database", "status": "success", "connection_pool_size": 10, "connection_timeout": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3981853, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 98, "message": "Voice agent initialized", "taskName": null, "event": "voice_agent_init", "component": "voice_agent", "status": "success", "model": "gpt-4", "voice_provider": "elevenlabs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3987277, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_1230", "phone_number": "+13568813157", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3996325, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_1230", "phone_number": "+13568813157", "call_duration": 338, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.3996325, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_2537", "phone_number": "+18947042689", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011595.4009478, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_2537", "phone_number": "+18947042689", "call_duration": 538, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011595.4009478, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_3309", "phone_number": "+12505404646", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011596.4022117, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_3309", "phone_number": "+12505404646", "call_duration": 220, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011596.4022117, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_7483", "phone_number": "+16894711062", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011597.404627, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_7483", "phone_number": "+16894711062", "call_duration": 530, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011597.404627, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_8972", "phone_number": "+17460820688", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4053862, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_8972", "phone_number": "+17460820688", "call_duration": 70, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4053862, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 133, "message": "Database connection lost", "taskName": null, "event": "database_error", "component": "database", "status": "error", "error_type": "ConnectionError", "error_message": "Connection timeout after 30 seconds", "retry_attempt": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4053862, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 142, "message": "Voice synthesis failed", "taskName": null, "event": "voice_synthesis_error", "component": "voice_agent", "status": "error", "error_type": "APIError", "provider": "elevenlabs", "error_code": 429, "rate_limit_exceeded": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4053862, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 153, "message": "Performance metrics collected", "taskName": null, "event": "metrics_collection", "component": "metrics", "status": "success", "cpu_usage": 37, "memory_usage": 44, "active_calls": 5, "response_time_ms": 175, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.4053862, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 163, "message": "Test logging completed", "taskName": null, "event": "test_completed", "component": "test", "status": "success", "total_logs_generated": 30, "test_duration": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5306144, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_cJXAobJywMXz", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5329309, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_cJXAobJywMXz", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011759.1175373, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011759.1185377, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.4431546, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.4468539, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.4506, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.4540193, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.456577, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.4601948, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.4641414, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.4693472, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.4844718, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.4920363, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.498284, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 74, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011761.503257, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.3828561, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.3861914, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 1.94, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5101116, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 59, "elapsed_time": 2.06, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5104353, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5102735, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5135844, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 71, "elapsed_time": 2.04, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5138338, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5209994, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 78, "elapsed_time": 2.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5406787, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 74, "elapsed_time": 2.04, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5406432, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5406792, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5420923, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 69, "elapsed_time": 2.07, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.546109, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5543401, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 76, "elapsed_time": 2.06, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5601203, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 65, "elapsed_time": 2.1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.561834, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 67, "elapsed_time": 2.1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.56101, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5601664, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.5658793, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 61, "elapsed_time": 2.11, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.566118, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.6005394, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 63, "elapsed_time": 2.15, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.600874, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.6623883, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 73, "elapsed_time": 2.18, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.6625276, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011763.6667063, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011764.097265, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_a5CFjiyoS6JV", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "India", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011950.084943, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-142", "id": "AW_a5CFjiyoS6JV", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011950.087249, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-143", "id": "AW_a5CFjiyoS6JV", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011950.2702203, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Future exception was never retrieved\nfuture: <Future finished exception=ClientConnectionError('Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)')>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/asyncio/sslproto.py\", line 651, in _do_shutdown\n    self._sslobj.unwrap()\n  File \"/usr/local/lib/python3.12/ssl.py\", line 920, in unwrap\n    return self._sslobj.shutdown()\n           ^^^^^^^^^^^^^^^^^^^^^^^\nssl.SSLError: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)\n\nThe above exception was the direct cause of the following exception:\n\naiohttp.client_exceptions.ClientConnectionError: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
